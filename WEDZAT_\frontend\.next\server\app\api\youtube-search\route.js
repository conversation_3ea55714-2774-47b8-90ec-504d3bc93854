/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/youtube-search/route";
exports.ids = ["app/api/youtube-search/route"];
exports.modules = {

/***/ "(rsc)/./app/api/youtube-search/route.ts":
/*!*****************************************!*\
  !*** ./app/api/youtube-search/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// Define the API endpoint URL\nconst API_URL = 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test';\nasync function GET(request) {\n    try {\n        // Get the search query and other parameters from the URL\n        const searchParams = request.nextUrl.searchParams;\n        const query = searchParams.get('q');\n        const page = searchParams.get('page') || '1';\n        const pageSize = searchParams.get('page_size') || '10';\n        const videoType = searchParams.get('type');\n        // Validate the search query\n        if (!query) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Search query is required'\n            }, {\n                status: 400\n            });\n        }\n        // Get the auth token from cookies or authorization header\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        let token = cookieStore.get('token')?.value || cookieStore.get('jwt_token')?.value || cookieStore.get('auth_token')?.value || cookieStore.get('wedzat_token')?.value;\n        // Also check authorization header\n        if (!token) {\n            const authHeader = request.headers.get('authorization');\n            if (authHeader && authHeader.startsWith('Bearer ')) {\n                token = authHeader.split(' ')[1];\n            }\n        }\n        if (!token) {\n            console.error('No authentication token found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        // Ensure token doesn't have Bearer prefix\n        if (token.startsWith('Bearer ')) {\n            token = token.substring(7);\n        }\n        // Validate token format (simple check for JWT format)\n        const isValidJWT = (token)=>{\n            const jwtRegex = /^[A-Za-z0-9-_=]+\\.[A-Za-z0-9-_=]+\\.[A-Za-z0-9-_.+/=]*$/;\n            return jwtRegex.test(token);\n        };\n        if (!isValidJWT(token)) {\n            console.error('Invalid token format:', token.substring(0, 15) + '...');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid authentication token'\n            }, {\n                status: 401\n            });\n        }\n        console.log('Using token for API request:', token.substring(0, 10) + '...');\n        // Build the API URL with query parameters\n        let apiUrl = `${API_URL}/hub/youtube-search?q=${encodeURIComponent(query)}&page=${page}&page_size=${pageSize}`;\n        // Add video type filter if specified\n        if (videoType) {\n            apiUrl += `&type=${encodeURIComponent(videoType)}`;\n        }\n        console.log('Making API request to:', apiUrl);\n        try {\n            // Call the backend API\n            const response = await fetch(apiUrl, {\n                headers: {\n                    'Authorization': `Bearer ${token}`\n                },\n                cache: 'no-store'\n            });\n            console.log('API response status:', response.status);\n            // Parse the response\n            const data = await response.json();\n            console.log('API response data:', JSON.stringify(data).substring(0, 200) + '...');\n            // Check for errors\n            if (!response.ok) {\n                console.error('API error:', data);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: data.error || 'Failed to search videos'\n                }, {\n                    status: response.status\n                });\n            }\n            // Return the search results\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        } catch (fetchError) {\n            console.error('Fetch error:', fetchError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Error connecting to API: ${fetchError.message}`\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Error searching videos:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/youtube-search/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fyoutube-search%2Froute&page=%2Fapi%2Fyoutube-search%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fyoutube-search%2Froute.ts&appDir=C%3A%5CUsers%5Cdevin%5CDownloads%5Cwedzatttt%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdevin%5CDownloads%5Cwedzatttt%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fyoutube-search%2Froute&page=%2Fapi%2Fyoutube-search%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fyoutube-search%2Froute.ts&appDir=C%3A%5CUsers%5Cdevin%5CDownloads%5Cwedzatttt%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdevin%5CDownloads%5Cwedzatttt%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_devin_Downloads_wedzatttt_WEDZAT_frontend_app_api_youtube_search_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/youtube-search/route.ts */ \"(rsc)/./app/api/youtube-search/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/youtube-search/route\",\n        pathname: \"/api/youtube-search\",\n        filename: \"route\",\n        bundlePath: \"app/api/youtube-search/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\api\\\\youtube-search\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_devin_Downloads_wedzatttt_WEDZAT_frontend_app_api_youtube_search_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZ5b3V0dWJlLXNlYXJjaCUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGeW91dHViZS1zZWFyY2glMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZ5b3V0dWJlLXNlYXJjaCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNkZXZpbiU1Q0Rvd25sb2FkcyU1Q3dlZHphdHR0dCU1Q1dFRFpBVF8lNUNmcm9udGVuZCU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDZGV2aW4lNUNEb3dubG9hZHMlNUN3ZWR6YXR0dHQlNUNXRURaQVRfJTVDZnJvbnRlbmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQzZDO0FBQzFIO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxkZXZpblxcXFxEb3dubG9hZHNcXFxcd2VkemF0dHR0XFxcXFdFRFpBVF9cXFxcZnJvbnRlbmRcXFxcYXBwXFxcXGFwaVxcXFx5b3V0dWJlLXNlYXJjaFxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkveW91dHViZS1zZWFyY2gvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS95b3V0dWJlLXNlYXJjaFwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkveW91dHViZS1zZWFyY2gvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxkZXZpblxcXFxEb3dubG9hZHNcXFxcd2VkemF0dHR0XFxcXFdFRFpBVF9cXFxcZnJvbnRlbmRcXFxcYXBwXFxcXGFwaVxcXFx5b3V0dWJlLXNlYXJjaFxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fyoutube-search%2Froute&page=%2Fapi%2Fyoutube-search%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fyoutube-search%2Froute.ts&appDir=C%3A%5CUsers%5Cdevin%5CDownloads%5Cwedzatttt%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdevin%5CDownloads%5Cwedzatttt%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fyoutube-search%2Froute&page=%2Fapi%2Fyoutube-search%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fyoutube-search%2Froute.ts&appDir=C%3A%5CUsers%5Cdevin%5CDownloads%5Cwedzatttt%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdevin%5CDownloads%5Cwedzatttt%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();