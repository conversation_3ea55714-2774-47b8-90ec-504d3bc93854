"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tinycolor2";
exports.ids = ["vendor-chunks/tinycolor2"];
exports.modules = {

/***/ "(ssr)/./node_modules/tinycolor2/esm/tinycolor.js":
/*!**************************************************!*\
  !*** ./node_modules/tinycolor2/esm/tinycolor.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ tinycolor)\n/* harmony export */ });\n// This file is autogenerated. It's used to publish ESM to npm.\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\n// https://github.com/bgrins/TinyColor\n// Brian Grinstead, MIT License\n\nvar trimLeft = /^\\s+/;\nvar trimRight = /\\s+$/;\nfunction tinycolor(color, opts) {\n  color = color ? color : \"\";\n  opts = opts || {};\n\n  // If input is already a tinycolor, return itself\n  if (color instanceof tinycolor) {\n    return color;\n  }\n  // If we are called as a function, call using new instead\n  if (!(this instanceof tinycolor)) {\n    return new tinycolor(color, opts);\n  }\n  var rgb = inputToRGB(color);\n  this._originalInput = color, this._r = rgb.r, this._g = rgb.g, this._b = rgb.b, this._a = rgb.a, this._roundA = Math.round(100 * this._a) / 100, this._format = opts.format || rgb.format;\n  this._gradientType = opts.gradientType;\n\n  // Don't let the range of [0,255] come back in [0,1].\n  // Potentially lose a little bit of precision here, but will fix issues where\n  // .5 gets interpreted as half of the total, instead of half of 1\n  // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n  if (this._r < 1) this._r = Math.round(this._r);\n  if (this._g < 1) this._g = Math.round(this._g);\n  if (this._b < 1) this._b = Math.round(this._b);\n  this._ok = rgb.ok;\n}\ntinycolor.prototype = {\n  isDark: function isDark() {\n    return this.getBrightness() < 128;\n  },\n  isLight: function isLight() {\n    return !this.isDark();\n  },\n  isValid: function isValid() {\n    return this._ok;\n  },\n  getOriginalInput: function getOriginalInput() {\n    return this._originalInput;\n  },\n  getFormat: function getFormat() {\n    return this._format;\n  },\n  getAlpha: function getAlpha() {\n    return this._a;\n  },\n  getBrightness: function getBrightness() {\n    //http://www.w3.org/TR/AERT#color-contrast\n    var rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n  },\n  getLuminance: function getLuminance() {\n    //http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    var rgb = this.toRgb();\n    var RsRGB, GsRGB, BsRGB, R, G, B;\n    RsRGB = rgb.r / 255;\n    GsRGB = rgb.g / 255;\n    BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928) R = RsRGB / 12.92;else R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    if (GsRGB <= 0.03928) G = GsRGB / 12.92;else G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    if (BsRGB <= 0.03928) B = BsRGB / 12.92;else B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  },\n  setAlpha: function setAlpha(value) {\n    this._a = boundAlpha(value);\n    this._roundA = Math.round(100 * this._a) / 100;\n    return this;\n  },\n  toHsv: function toHsv() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this._a\n    };\n  },\n  toHsvString: function toHsvString() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    var h = Math.round(hsv.h * 360),\n      s = Math.round(hsv.s * 100),\n      v = Math.round(hsv.v * 100);\n    return this._a == 1 ? \"hsv(\" + h + \", \" + s + \"%, \" + v + \"%)\" : \"hsva(\" + h + \", \" + s + \"%, \" + v + \"%, \" + this._roundA + \")\";\n  },\n  toHsl: function toHsl() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this._a\n    };\n  },\n  toHslString: function toHslString() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    var h = Math.round(hsl.h * 360),\n      s = Math.round(hsl.s * 100),\n      l = Math.round(hsl.l * 100);\n    return this._a == 1 ? \"hsl(\" + h + \", \" + s + \"%, \" + l + \"%)\" : \"hsla(\" + h + \", \" + s + \"%, \" + l + \"%, \" + this._roundA + \")\";\n  },\n  toHex: function toHex(allow3Char) {\n    return rgbToHex(this._r, this._g, this._b, allow3Char);\n  },\n  toHexString: function toHexString(allow3Char) {\n    return \"#\" + this.toHex(allow3Char);\n  },\n  toHex8: function toHex8(allow4Char) {\n    return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);\n  },\n  toHex8String: function toHex8String(allow4Char) {\n    return \"#\" + this.toHex8(allow4Char);\n  },\n  toRgb: function toRgb() {\n    return {\n      r: Math.round(this._r),\n      g: Math.round(this._g),\n      b: Math.round(this._b),\n      a: this._a\n    };\n  },\n  toRgbString: function toRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \")\" : \"rgba(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \", \" + this._roundA + \")\";\n  },\n  toPercentageRgb: function toPercentageRgb() {\n    return {\n      r: Math.round(bound01(this._r, 255) * 100) + \"%\",\n      g: Math.round(bound01(this._g, 255) * 100) + \"%\",\n      b: Math.round(bound01(this._b, 255) * 100) + \"%\",\n      a: this._a\n    };\n  },\n  toPercentageRgbString: function toPercentageRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%)\" : \"rgba(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%, \" + this._roundA + \")\";\n  },\n  toName: function toName() {\n    if (this._a === 0) {\n      return \"transparent\";\n    }\n    if (this._a < 1) {\n      return false;\n    }\n    return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;\n  },\n  toFilter: function toFilter(secondColor) {\n    var hex8String = \"#\" + rgbaToArgbHex(this._r, this._g, this._b, this._a);\n    var secondHex8String = hex8String;\n    var gradientType = this._gradientType ? \"GradientType = 1, \" : \"\";\n    if (secondColor) {\n      var s = tinycolor(secondColor);\n      secondHex8String = \"#\" + rgbaToArgbHex(s._r, s._g, s._b, s._a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\" + gradientType + \"startColorstr=\" + hex8String + \",endColorstr=\" + secondHex8String + \")\";\n  },\n  toString: function toString(format) {\n    var formatSet = !!format;\n    format = format || this._format;\n    var formattedString = false;\n    var hasAlpha = this._a < 1 && this._a >= 0;\n    var needsAlphaFormat = !formatSet && hasAlpha && (format === \"hex\" || format === \"hex6\" || format === \"hex3\" || format === \"hex4\" || format === \"hex8\" || format === \"name\");\n    if (needsAlphaFormat) {\n      // Special case for \"transparent\", all other non-alpha formats\n      // will return rgba when there is transparency.\n      if (format === \"name\" && this._a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === \"rgb\") {\n      formattedString = this.toRgbString();\n    }\n    if (format === \"prgb\") {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === \"hex\" || format === \"hex6\") {\n      formattedString = this.toHexString();\n    }\n    if (format === \"hex3\") {\n      formattedString = this.toHexString(true);\n    }\n    if (format === \"hex4\") {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === \"hex8\") {\n      formattedString = this.toHex8String();\n    }\n    if (format === \"name\") {\n      formattedString = this.toName();\n    }\n    if (format === \"hsl\") {\n      formattedString = this.toHslString();\n    }\n    if (format === \"hsv\") {\n      formattedString = this.toHsvString();\n    }\n    return formattedString || this.toHexString();\n  },\n  clone: function clone() {\n    return tinycolor(this.toString());\n  },\n  _applyModification: function _applyModification(fn, args) {\n    var color = fn.apply(null, [this].concat([].slice.call(args)));\n    this._r = color._r;\n    this._g = color._g;\n    this._b = color._b;\n    this.setAlpha(color._a);\n    return this;\n  },\n  lighten: function lighten() {\n    return this._applyModification(_lighten, arguments);\n  },\n  brighten: function brighten() {\n    return this._applyModification(_brighten, arguments);\n  },\n  darken: function darken() {\n    return this._applyModification(_darken, arguments);\n  },\n  desaturate: function desaturate() {\n    return this._applyModification(_desaturate, arguments);\n  },\n  saturate: function saturate() {\n    return this._applyModification(_saturate, arguments);\n  },\n  greyscale: function greyscale() {\n    return this._applyModification(_greyscale, arguments);\n  },\n  spin: function spin() {\n    return this._applyModification(_spin, arguments);\n  },\n  _applyCombination: function _applyCombination(fn, args) {\n    return fn.apply(null, [this].concat([].slice.call(args)));\n  },\n  analogous: function analogous() {\n    return this._applyCombination(_analogous, arguments);\n  },\n  complement: function complement() {\n    return this._applyCombination(_complement, arguments);\n  },\n  monochromatic: function monochromatic() {\n    return this._applyCombination(_monochromatic, arguments);\n  },\n  splitcomplement: function splitcomplement() {\n    return this._applyCombination(_splitcomplement, arguments);\n  },\n  // Disabled until https://github.com/bgrins/TinyColor/issues/254\n  // polyad: function (number) {\n  //   return this._applyCombination(polyad, [number]);\n  // },\n  triad: function triad() {\n    return this._applyCombination(polyad, [3]);\n  },\n  tetrad: function tetrad() {\n    return this._applyCombination(polyad, [4]);\n  }\n};\n\n// If input is an object, force 1 into \"1.0\" to handle ratios properly\n// String input requires \"1.0\" as input, so 1 will be treated as 1\ntinycolor.fromRatio = function (color, opts) {\n  if (_typeof(color) == \"object\") {\n    var newColor = {};\n    for (var i in color) {\n      if (color.hasOwnProperty(i)) {\n        if (i === \"a\") {\n          newColor[i] = color[i];\n        } else {\n          newColor[i] = convertToPercentage(color[i]);\n        }\n      }\n    }\n    color = newColor;\n  }\n  return tinycolor(color, opts);\n};\n\n// Given a string or object, convert that input to RGB\n// Possible string inputs:\n//\n//     \"red\"\n//     \"#f00\" or \"f00\"\n//     \"#ff0000\" or \"ff0000\"\n//     \"#ff000000\" or \"ff000000\"\n//     \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n//     \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n//     \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n//     \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n//     \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n//     \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n//     \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n//\nfunction inputToRGB(color) {\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  var a = 1;\n  var s = null;\n  var v = null;\n  var l = null;\n  var ok = false;\n  var format = false;\n  if (typeof color == \"string\") {\n    color = stringInputToObject(color);\n  }\n  if (_typeof(color) == \"object\") {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === \"%\" ? \"prgb\" : \"rgb\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = \"hsv\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = \"hsl\";\n    }\n    if (color.hasOwnProperty(\"a\")) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok: ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a: a\n  };\n}\n\n// Conversion Functions\n// --------------------\n\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n\n// `rgbToRgb`\n// Handle bounds / percentage checking to conform to CSS color spec\n// <http://www.w3.org/TR/css3-color/>\n// *Assumes:* r, g, b in [0, 255] or [0, 1]\n// *Returns:* { r, g, b } in [0, 255]\nfunction rgbToRgb(r, g, b) {\n  return {\n    r: bound01(r, 255) * 255,\n    g: bound01(g, 255) * 255,\n    b: bound01(b, 255) * 255\n  };\n}\n\n// `rgbToHsl`\n// Converts an RGB color value to HSL.\n// *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n// *Returns:* { h, s, l } in [0,1]\nfunction rgbToHsl(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    l = (max + min) / 2;\n  if (max == min) {\n    h = s = 0; // achromatic\n  } else {\n    var d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    l: l\n  };\n}\n\n// `hslToRgb`\n// Converts an HSL color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hslToRgb(h, s, l) {\n  var r, g, b;\n  h = bound01(h, 360);\n  s = bound01(s, 100);\n  l = bound01(l, 100);\n  function hue2rgb(p, q, t) {\n    if (t < 0) t += 1;\n    if (t > 1) t -= 1;\n    if (t < 1 / 6) return p + (q - p) * 6 * t;\n    if (t < 1 / 2) return q;\n    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n  }\n  if (s === 0) {\n    r = g = b = l; // achromatic\n  } else {\n    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    var p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1 / 3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1 / 3);\n  }\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHsv`\n// Converts an RGB color value to HSV\n// *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n// *Returns:* { h, s, v } in [0,1]\nfunction rgbToHsv(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    v = max;\n  var d = max - min;\n  s = max === 0 ? 0 : d / max;\n  if (max == min) {\n    h = 0; // achromatic\n  } else {\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    v: v\n  };\n}\n\n// `hsvToRgb`\n// Converts an HSV color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hsvToRgb(h, s, v) {\n  h = bound01(h, 360) * 6;\n  s = bound01(s, 100);\n  v = bound01(v, 100);\n  var i = Math.floor(h),\n    f = h - i,\n    p = v * (1 - s),\n    q = v * (1 - f * s),\n    t = v * (1 - (1 - f) * s),\n    mod = i % 6,\n    r = [v, q, p, p, t, v][mod],\n    g = [t, v, v, q, p, p][mod],\n    b = [p, p, t, v, v, q][mod];\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHex`\n// Converts an RGB color to hex\n// Assumes r, g, and b are contained in the set [0, 255]\n// Returns a 3 or 6 character hex\nfunction rgbToHex(r, g, b, allow3Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n\n  // Return a 3 character hex if possible\n  if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToHex`\n// Converts an RGBA color plus alpha transparency to hex\n// Assumes r, g, b are contained in the set [0, 255] and\n// a in [0, 1]. Returns a 4 or 8 character rgba hex\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16)), pad2(convertDecimalToHex(a))];\n\n  // Return a 4 character hex if possible\n  if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToArgbHex`\n// Converts an RGBA color to an ARGB Hex8 string\n// Rarely used, but required for \"toFilter()\"\nfunction rgbaToArgbHex(r, g, b, a) {\n  var hex = [pad2(convertDecimalToHex(a)), pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  return hex.join(\"\");\n}\n\n// `equals`\n// Can be called with any tinycolor input\ntinycolor.equals = function (color1, color2) {\n  if (!color1 || !color2) return false;\n  return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();\n};\ntinycolor.random = function () {\n  return tinycolor.fromRatio({\n    r: Math.random(),\n    g: Math.random(),\n    b: Math.random()\n  });\n};\n\n// Modification Functions\n// ----------------------\n// Thanks to less.js for some of the basics here\n// <https://github.com/cloudhead/less.js/blob/master/lib/less/functions.js>\n\nfunction _desaturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s -= amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _saturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s += amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _greyscale(color) {\n  return tinycolor(color).desaturate(100);\n}\nfunction _lighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l += amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\nfunction _brighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var rgb = tinycolor(color).toRgb();\n  rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n  rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n  rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n  return tinycolor(rgb);\n}\nfunction _darken(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l -= amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\n\n// Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n// Values outside of this range will be wrapped into this range.\nfunction _spin(color, amount) {\n  var hsl = tinycolor(color).toHsl();\n  var hue = (hsl.h + amount) % 360;\n  hsl.h = hue < 0 ? 360 + hue : hue;\n  return tinycolor(hsl);\n}\n\n// Combination Functions\n// ---------------------\n// Thanks to jQuery xColor for some of the ideas behind these\n// <https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js>\n\nfunction _complement(color) {\n  var hsl = tinycolor(color).toHsl();\n  hsl.h = (hsl.h + 180) % 360;\n  return tinycolor(hsl);\n}\nfunction polyad(color, number) {\n  if (isNaN(number) || number <= 0) {\n    throw new Error(\"Argument to polyad must be a positive number\");\n  }\n  var hsl = tinycolor(color).toHsl();\n  var result = [tinycolor(color)];\n  var step = 360 / number;\n  for (var i = 1; i < number; i++) {\n    result.push(tinycolor({\n      h: (hsl.h + i * step) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }));\n  }\n  return result;\n}\nfunction _splitcomplement(color) {\n  var hsl = tinycolor(color).toHsl();\n  var h = hsl.h;\n  return [tinycolor(color), tinycolor({\n    h: (h + 72) % 360,\n    s: hsl.s,\n    l: hsl.l\n  }), tinycolor({\n    h: (h + 216) % 360,\n    s: hsl.s,\n    l: hsl.l\n  })];\n}\nfunction _analogous(color, results, slices) {\n  results = results || 6;\n  slices = slices || 30;\n  var hsl = tinycolor(color).toHsl();\n  var part = 360 / slices;\n  var ret = [tinycolor(color)];\n  for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n    hsl.h = (hsl.h + part) % 360;\n    ret.push(tinycolor(hsl));\n  }\n  return ret;\n}\nfunction _monochromatic(color, results) {\n  results = results || 6;\n  var hsv = tinycolor(color).toHsv();\n  var h = hsv.h,\n    s = hsv.s,\n    v = hsv.v;\n  var ret = [];\n  var modification = 1 / results;\n  while (results--) {\n    ret.push(tinycolor({\n      h: h,\n      s: s,\n      v: v\n    }));\n    v = (v + modification) % 1;\n  }\n  return ret;\n}\n\n// Utility Functions\n// ---------------------\n\ntinycolor.mix = function (color1, color2, amount) {\n  amount = amount === 0 ? 0 : amount || 50;\n  var rgb1 = tinycolor(color1).toRgb();\n  var rgb2 = tinycolor(color2).toRgb();\n  var p = amount / 100;\n  var rgba = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b,\n    a: (rgb2.a - rgb1.a) * p + rgb1.a\n  };\n  return tinycolor(rgba);\n};\n\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n\n// `contrast`\n// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\ntinycolor.readability = function (color1, color2) {\n  var c1 = tinycolor(color1);\n  var c2 = tinycolor(color2);\n  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n};\n\n// `isReadable`\n// Ensure that foreground and background color combinations meet WCAG2 guidelines.\n// The third argument is an optional Object.\n//      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n//      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n// If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n\n// *Example*\n//    tinycolor.isReadable(\"#000\", \"#111\") => false\n//    tinycolor.isReadable(\"#000\", \"#111\",{level:\"AA\",size:\"large\"}) => false\ntinycolor.isReadable = function (color1, color2, wcag2) {\n  var readability = tinycolor.readability(color1, color2);\n  var wcag2Parms, out;\n  out = false;\n  wcag2Parms = validateWCAG2Parms(wcag2);\n  switch (wcag2Parms.level + wcag2Parms.size) {\n    case \"AAsmall\":\n    case \"AAAlarge\":\n      out = readability >= 4.5;\n      break;\n    case \"AAlarge\":\n      out = readability >= 3;\n      break;\n    case \"AAAsmall\":\n      out = readability >= 7;\n      break;\n  }\n  return out;\n};\n\n// `mostReadable`\n// Given a base color and a list of possible foreground or background\n// colors for that base, returns the most readable color.\n// Optionally returns Black or White if the most readable color is unreadable.\n// *Example*\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:false}).toHexString(); // \"#112255\"\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:true}).toHexString();  // \"#ffffff\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"large\"}).toHexString(); // \"#faf3f3\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"small\"}).toHexString(); // \"#ffffff\"\ntinycolor.mostReadable = function (baseColor, colorList, args) {\n  var bestColor = null;\n  var bestScore = 0;\n  var readability;\n  var includeFallbackColors, level, size;\n  args = args || {};\n  includeFallbackColors = args.includeFallbackColors;\n  level = args.level;\n  size = args.size;\n  for (var i = 0; i < colorList.length; i++) {\n    readability = tinycolor.readability(baseColor, colorList[i]);\n    if (readability > bestScore) {\n      bestScore = readability;\n      bestColor = tinycolor(colorList[i]);\n    }\n  }\n  if (tinycolor.isReadable(baseColor, bestColor, {\n    level: level,\n    size: size\n  }) || !includeFallbackColors) {\n    return bestColor;\n  } else {\n    args.includeFallbackColors = false;\n    return tinycolor.mostReadable(baseColor, [\"#fff\", \"#000\"], args);\n  }\n};\n\n// Big List of Colors\n// ------------------\n// <https://www.w3.org/TR/css-color-4/#named-colors>\nvar names = tinycolor.names = {\n  aliceblue: \"f0f8ff\",\n  antiquewhite: \"faebd7\",\n  aqua: \"0ff\",\n  aquamarine: \"7fffd4\",\n  azure: \"f0ffff\",\n  beige: \"f5f5dc\",\n  bisque: \"ffe4c4\",\n  black: \"000\",\n  blanchedalmond: \"ffebcd\",\n  blue: \"00f\",\n  blueviolet: \"8a2be2\",\n  brown: \"a52a2a\",\n  burlywood: \"deb887\",\n  burntsienna: \"ea7e5d\",\n  cadetblue: \"5f9ea0\",\n  chartreuse: \"7fff00\",\n  chocolate: \"d2691e\",\n  coral: \"ff7f50\",\n  cornflowerblue: \"6495ed\",\n  cornsilk: \"fff8dc\",\n  crimson: \"dc143c\",\n  cyan: \"0ff\",\n  darkblue: \"00008b\",\n  darkcyan: \"008b8b\",\n  darkgoldenrod: \"b8860b\",\n  darkgray: \"a9a9a9\",\n  darkgreen: \"006400\",\n  darkgrey: \"a9a9a9\",\n  darkkhaki: \"bdb76b\",\n  darkmagenta: \"8b008b\",\n  darkolivegreen: \"556b2f\",\n  darkorange: \"ff8c00\",\n  darkorchid: \"9932cc\",\n  darkred: \"8b0000\",\n  darksalmon: \"e9967a\",\n  darkseagreen: \"8fbc8f\",\n  darkslateblue: \"483d8b\",\n  darkslategray: \"2f4f4f\",\n  darkslategrey: \"2f4f4f\",\n  darkturquoise: \"00ced1\",\n  darkviolet: \"9400d3\",\n  deeppink: \"ff1493\",\n  deepskyblue: \"00bfff\",\n  dimgray: \"696969\",\n  dimgrey: \"696969\",\n  dodgerblue: \"1e90ff\",\n  firebrick: \"b22222\",\n  floralwhite: \"fffaf0\",\n  forestgreen: \"228b22\",\n  fuchsia: \"f0f\",\n  gainsboro: \"dcdcdc\",\n  ghostwhite: \"f8f8ff\",\n  gold: \"ffd700\",\n  goldenrod: \"daa520\",\n  gray: \"808080\",\n  green: \"008000\",\n  greenyellow: \"adff2f\",\n  grey: \"808080\",\n  honeydew: \"f0fff0\",\n  hotpink: \"ff69b4\",\n  indianred: \"cd5c5c\",\n  indigo: \"4b0082\",\n  ivory: \"fffff0\",\n  khaki: \"f0e68c\",\n  lavender: \"e6e6fa\",\n  lavenderblush: \"fff0f5\",\n  lawngreen: \"7cfc00\",\n  lemonchiffon: \"fffacd\",\n  lightblue: \"add8e6\",\n  lightcoral: \"f08080\",\n  lightcyan: \"e0ffff\",\n  lightgoldenrodyellow: \"fafad2\",\n  lightgray: \"d3d3d3\",\n  lightgreen: \"90ee90\",\n  lightgrey: \"d3d3d3\",\n  lightpink: \"ffb6c1\",\n  lightsalmon: \"ffa07a\",\n  lightseagreen: \"20b2aa\",\n  lightskyblue: \"87cefa\",\n  lightslategray: \"789\",\n  lightslategrey: \"789\",\n  lightsteelblue: \"b0c4de\",\n  lightyellow: \"ffffe0\",\n  lime: \"0f0\",\n  limegreen: \"32cd32\",\n  linen: \"faf0e6\",\n  magenta: \"f0f\",\n  maroon: \"800000\",\n  mediumaquamarine: \"66cdaa\",\n  mediumblue: \"0000cd\",\n  mediumorchid: \"ba55d3\",\n  mediumpurple: \"9370db\",\n  mediumseagreen: \"3cb371\",\n  mediumslateblue: \"7b68ee\",\n  mediumspringgreen: \"00fa9a\",\n  mediumturquoise: \"48d1cc\",\n  mediumvioletred: \"c71585\",\n  midnightblue: \"191970\",\n  mintcream: \"f5fffa\",\n  mistyrose: \"ffe4e1\",\n  moccasin: \"ffe4b5\",\n  navajowhite: \"ffdead\",\n  navy: \"000080\",\n  oldlace: \"fdf5e6\",\n  olive: \"808000\",\n  olivedrab: \"6b8e23\",\n  orange: \"ffa500\",\n  orangered: \"ff4500\",\n  orchid: \"da70d6\",\n  palegoldenrod: \"eee8aa\",\n  palegreen: \"98fb98\",\n  paleturquoise: \"afeeee\",\n  palevioletred: \"db7093\",\n  papayawhip: \"ffefd5\",\n  peachpuff: \"ffdab9\",\n  peru: \"cd853f\",\n  pink: \"ffc0cb\",\n  plum: \"dda0dd\",\n  powderblue: \"b0e0e6\",\n  purple: \"800080\",\n  rebeccapurple: \"663399\",\n  red: \"f00\",\n  rosybrown: \"bc8f8f\",\n  royalblue: \"4169e1\",\n  saddlebrown: \"8b4513\",\n  salmon: \"fa8072\",\n  sandybrown: \"f4a460\",\n  seagreen: \"2e8b57\",\n  seashell: \"fff5ee\",\n  sienna: \"a0522d\",\n  silver: \"c0c0c0\",\n  skyblue: \"87ceeb\",\n  slateblue: \"6a5acd\",\n  slategray: \"708090\",\n  slategrey: \"708090\",\n  snow: \"fffafa\",\n  springgreen: \"00ff7f\",\n  steelblue: \"4682b4\",\n  tan: \"d2b48c\",\n  teal: \"008080\",\n  thistle: \"d8bfd8\",\n  tomato: \"ff6347\",\n  turquoise: \"40e0d0\",\n  violet: \"ee82ee\",\n  wheat: \"f5deb3\",\n  white: \"fff\",\n  whitesmoke: \"f5f5f5\",\n  yellow: \"ff0\",\n  yellowgreen: \"9acd32\"\n};\n\n// Make it easy to access colors via `hexNames[hex]`\nvar hexNames = tinycolor.hexNames = flip(names);\n\n// Utilities\n// ---------\n\n// `{ 'name1': 'val1' }` becomes `{ 'val1': 'name1' }`\nfunction flip(o) {\n  var flipped = {};\n  for (var i in o) {\n    if (o.hasOwnProperty(i)) {\n      flipped[o[i]] = i;\n    }\n  }\n  return flipped;\n}\n\n// Return a valid alpha value [0,1] with all invalid values being set to 1\nfunction boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\n\n// Take input from [0, n] and return it as [0, 1]\nfunction bound01(n, max) {\n  if (isOnePointZero(n)) n = \"100%\";\n  var processPercent = isPercentage(n);\n  n = Math.min(max, Math.max(0, parseFloat(n)));\n\n  // Automatically convert percentage into number\n  if (processPercent) {\n    n = parseInt(n * max, 10) / 100;\n  }\n\n  // Handle floating point rounding errors\n  if (Math.abs(n - max) < 0.000001) {\n    return 1;\n  }\n\n  // Convert into [0, 1] range if it isn't already\n  return n % max / parseFloat(max);\n}\n\n// Force a number between 0 and 1\nfunction clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\n\n// Parse a base-16 hex value into a base-10 integer\nfunction parseIntFromHex(val) {\n  return parseInt(val, 16);\n}\n\n// Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n// <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\nfunction isOnePointZero(n) {\n  return typeof n == \"string\" && n.indexOf(\".\") != -1 && parseFloat(n) === 1;\n}\n\n// Check to see if string passed in is a percentage\nfunction isPercentage(n) {\n  return typeof n === \"string\" && n.indexOf(\"%\") != -1;\n}\n\n// Force a hex value to have 2 characters\nfunction pad2(c) {\n  return c.length == 1 ? \"0\" + c : \"\" + c;\n}\n\n// Replace a decimal with it's percentage value\nfunction convertToPercentage(n) {\n  if (n <= 1) {\n    n = n * 100 + \"%\";\n  }\n  return n;\n}\n\n// Converts a decimal to a hex value\nfunction convertDecimalToHex(d) {\n  return Math.round(parseFloat(d) * 255).toString(16);\n}\n// Converts a hex value to a decimal\nfunction convertHexToDecimal(h) {\n  return parseIntFromHex(h) / 255;\n}\nvar matchers = function () {\n  // <http://www.w3.org/TR/css3-values/#integers>\n  var CSS_INTEGER = \"[-\\\\+]?\\\\d+%?\";\n\n  // <http://www.w3.org/TR/css3-values/#number-value>\n  var CSS_NUMBER = \"[-\\\\+]?\\\\d*\\\\.\\\\d+%?\";\n\n  // Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\n  var CSS_UNIT = \"(?:\" + CSS_NUMBER + \")|(?:\" + CSS_INTEGER + \")\";\n\n  // Actual matching.\n  // Parentheses and commas are optional, but not required.\n  // Whitespace can take the place of commas or opening paren\n  var PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  var PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  return {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp(\"rgb\" + PERMISSIVE_MATCH3),\n    rgba: new RegExp(\"rgba\" + PERMISSIVE_MATCH4),\n    hsl: new RegExp(\"hsl\" + PERMISSIVE_MATCH3),\n    hsla: new RegExp(\"hsla\" + PERMISSIVE_MATCH4),\n    hsv: new RegExp(\"hsv\" + PERMISSIVE_MATCH3),\n    hsva: new RegExp(\"hsva\" + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n  };\n}();\n\n// `isValidCSSUnit`\n// Take in a single string / number and check to see if it looks like a CSS unit\n// (see `matchers` above for definition).\nfunction isValidCSSUnit(color) {\n  return !!matchers.CSS_UNIT.exec(color);\n}\n\n// `stringInputToObject`\n// Permissive string parsing.  Take in a number of formats, and output an object\n// based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\nfunction stringInputToObject(color) {\n  color = color.replace(trimLeft, \"\").replace(trimRight, \"\").toLowerCase();\n  var named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color == \"transparent\") {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: \"name\"\n    };\n  }\n\n  // Try to match string input using regular expressions.\n  // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n  // Just return an object and let the conversion functions handle that.\n  // This way the result will be the same whether the tinycolor is initialized with string or object.\n  var match;\n  if (match = matchers.rgb.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  if (match = matchers.rgba.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsl.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  if (match = matchers.hsla.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsv.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  if (match = matchers.hsva.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hex8.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex6.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  if (match = matchers.hex4.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      a: convertHexToDecimal(match[4] + \"\" + match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex3.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  return false;\n}\nfunction validateWCAG2Parms(parms) {\n  // return valid WCAG2 parms for isReadable.\n  // If input parms are invalid, return {\"level\":\"AA\", \"size\":\"small\"}\n  var level, size;\n  parms = parms || {\n    level: \"AA\",\n    size: \"small\"\n  };\n  level = (parms.level || \"AA\").toUpperCase();\n  size = (parms.size || \"small\").toLowerCase();\n  if (level !== \"AA\" && level !== \"AAA\") {\n    level = \"AA\";\n  }\n  if (size !== \"small\" && size !== \"large\") {\n    size = \"small\";\n  }\n  return {\n    level: level,\n    size: size\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tinycolor2/esm/tinycolor.js\n");

/***/ })

};
;