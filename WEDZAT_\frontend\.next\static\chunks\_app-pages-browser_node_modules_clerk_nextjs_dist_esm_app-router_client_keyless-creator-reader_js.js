"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_clerk_nextjs_dist_esm_app-router_client_keyless-creator-reader_js"],{

/***/ "(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeylessCreatorOrReader: () => (/* binding */ KeylessCreatorOrReader)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _keyless_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../keyless-actions */ \"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\");\n\n\n\n\nconst KeylessCreatorOrReader = (props) => {\n  var _a;\n  const { children } = props;\n  const segments = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useSelectedLayoutSegments)();\n  const isNotFoundRoute = ((_a = segments[0]) == null ? void 0 : _a.startsWith(\"/_not-found\")) || false;\n  const [state, fetchKeys] = react__WEBPACK_IMPORTED_MODULE_1___default().useActionState(_keyless_actions__WEBPACK_IMPORTED_MODULE_2__.createOrReadKeylessAction, null);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    if (isNotFoundRoute) {\n      return;\n    }\n    react__WEBPACK_IMPORTED_MODULE_1___default().startTransition(() => {\n      fetchKeys();\n    });\n  }, [isNotFoundRoute]);\n  if (!react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(children)) {\n    return children;\n  }\n  return react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(children, {\n    key: state == null ? void 0 : state.publishableKey,\n    publishableKey: state == null ? void 0 : state.publishableKey,\n    __internal_keyless_claimKeylessApplicationUrl: state == null ? void 0 : state.claimUrl,\n    __internal_keyless_copyInstanceKeysUrl: state == null ? void 0 : state.apiKeysUrl,\n    __internal_bypassMissingPublishableKey: true\n  });\n};\n\n//# sourceMappingURL=keyless-creator-reader.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOrReadKeylessAction: () => (/* binding */ createOrReadKeylessAction),\n/* harmony export */   deleteKeylessAction: () => (/* binding */ deleteKeylessAction),\n/* harmony export */   syncKeylessConfigAction: () => (/* binding */ syncKeylessConfigAction)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"7f2d2de1ad00c916bb603b463ac2f32292685e82f5\":\"deleteKeylessAction\",\"7fb21dbfccad919a7cf97d464eb9fa66ef032da11c\":\"syncKeylessConfigAction\",\"7ff39c4e6ea16f9a15b3903f9fc988ea2d8a541b30\":\"createOrReadKeylessAction\"} */ \nvar createOrReadKeylessAction = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7ff39c4e6ea16f9a15b3903f9fc988ea2d8a541b30\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"createOrReadKeylessAction\");\nvar deleteKeylessAction = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7f2d2de1ad00c916bb603b463ac2f32292685e82f5\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"deleteKeylessAction\");\nvar syncKeylessConfigAction = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7fb21dbfccad919a7cf97d464eb9fa66ef032da11c\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"syncKeylessConfigAction\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\n"));

/***/ })

}]);