"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/search/page",{

/***/ "(app-pages-browser)/./app/search/page.tsx":
/*!*****************************!*\
  !*** ./app/search/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_VideoCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/VideoCard */ \"(app-pages-browser)/./components/VideoCard.tsx\");\n/* harmony import */ var _components_HomeDashboard_TopNav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/HomeDashboard/TopNav */ \"(app-pages-browser)/./components/HomeDashboard/TopNav.tsx\");\n/* harmony import */ var _components_HomeDashboard_LeftSideNavBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/HomeDashboard/LeftSideNavBar */ \"(app-pages-browser)/./components/HomeDashboard/LeftSideNavBar.tsx\");\n/* harmony import */ var _components_HomeDashboard_RightSideNavBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/HomeDashboard/RightSideNavBar */ \"(app-pages-browser)/./components/HomeDashboard/RightSideNavBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SearchPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const query = searchParams.get('q') || '';\n    const page = parseInt(searchParams.get('page') || '1');\n    const videoType = searchParams.get('type');\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [relatedVideos, setRelatedVideos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasNextPage, setHasNextPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(query);\n    // Fetch search results\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchPage.useEffect\": ()=>{\n            if (!query) return;\n            const fetchSearchResults = {\n                \"SearchPage.useEffect.fetchSearchResults\": async ()=>{\n                    setLoading(true);\n                    setError(null);\n                    try {\n                        // Get token from localStorage for authorization\n                        const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('auth_token') || localStorage.getItem('wedzat_token');\n                        if (!token) {\n                            setError('Authentication required. Please log in.');\n                            setLoading(false);\n                            return;\n                        }\n                        // Build the API URL with query parameters\n                        let apiUrl = \"/api/youtube-search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(page);\n                        // Add video type filter if specified\n                        if (videoType) {\n                            apiUrl += \"&type=\".concat(encodeURIComponent(videoType));\n                        }\n                        console.log('Making search request to:', apiUrl);\n                        const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(apiUrl, {\n                            headers: {\n                                'Authorization': \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('Search response:', response.data);\n                        if (!response.data.search_results) {\n                            console.error('No search_results in response:', response.data);\n                            setError('Invalid response format from server');\n                            return;\n                        }\n                        setSearchResults(response.data.search_results || []);\n                        setRelatedVideos(response.data.related_videos || []);\n                        setTotalCount(response.data.total_count || 0);\n                        setCurrentPage(response.data.current_page || 1);\n                        setHasNextPage(response.data.next_page || false);\n                    } catch (err) {\n                        var _err_response, _err_response1, _err_response_data, _err_response2;\n                        console.error('Error fetching search results:', err);\n                        console.error('Error details:', (_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data);\n                        if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 401) {\n                            setError('Authentication required. Please log in again.');\n                        } else if ((_err_response2 = err.response) === null || _err_response2 === void 0 ? void 0 : (_err_response_data = _err_response2.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) {\n                            setError(\"Error: \".concat(err.response.data.error));\n                        } else {\n                            setError(\"Failed to fetch search results: \".concat(err.message || 'Unknown error'));\n                        }\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SearchPage.useEffect.fetchSearchResults\"];\n            fetchSearchResults();\n        }\n    }[\"SearchPage.useEffect\"], [\n        query,\n        page,\n        videoType\n    ]);\n    // Handle search form submission\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (!searchInput.trim()) return;\n        // Navigate to search page with the new query\n        router.push(\"/search?q=\".concat(encodeURIComponent(searchInput.trim())));\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage < 1) return;\n        // Navigate to the new page\n        let url = \"/search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(newPage);\n        if (videoType) {\n            url += \"&type=\".concat(encodeURIComponent(videoType));\n        }\n        router.push(url);\n    };\n    // Handle video type filter\n    const handleTypeFilter = (type)=>{\n        let url = \"/search?q=\".concat(encodeURIComponent(query));\n        if (type) {\n            url += \"&type=\".concat(encodeURIComponent(type));\n        }\n        router.push(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_TopNav__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_LeftSideNavBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-4 md:p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchInput,\n                                            onChange: (e)=>setSearchInput(e.target.value),\n                                            placeholder: \"Search for videos...\",\n                                            className: \"w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 text-black\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"absolute left-3 top-3.5 h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"absolute right-3 top-2 px-4 py-1.5 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\",\n                                            children: \"Search\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter(null),\n                                        className: \"px-4 py-2 rounded-full text-sm \".concat(!videoType ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                        children: \"All Videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('flash'),\n                                        className: \"px-4 py-2 rounded-full text-sm \".concat(videoType === 'flash' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                        children: \"Flashes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('glimpse'),\n                                        className: \"px-4 py-2 rounded-full text-sm \".concat(videoType === 'glimpse' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                        children: \"Glimpses\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('movie'),\n                                        className: \"px-4 py-2 rounded-full text-sm \".concat(videoType === 'movie' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                        children: \"Movies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('story'),\n                                        className: \"px-4 py-2 rounded-full text-sm \".concat(videoType === 'story' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                        children: \"Stories\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-8 w-8 animate-spin text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-100 text-red-700 p-4 rounded-md mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold\",\n                                        children: \"Error:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.reload(),\n                                        className: \"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\",\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: [\n                                                totalCount,\n                                                \" \",\n                                                totalCount === 1 ? 'result' : 'results',\n                                                ' for \"',\n                                                query,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: searchResults.map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                video: video\n                                            }, video.video_id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mt-8 gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(currentPage - 1),\n                                                disabled: currentPage === 1,\n                                                className: \"px-4 py-2 rounded-md bg-gray-200 text-gray-700 disabled:opacity-50\",\n                                                children: \"Previous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-4 py-2\",\n                                                children: [\n                                                    \"Page \",\n                                                    currentPage\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(currentPage + 1),\n                                                disabled: !hasNextPage,\n                                                className: \"px-4 py-2 rounded-md bg-gray-200 text-gray-700 disabled:opacity-50\",\n                                                children: \"Next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    relatedVideos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold mb-4\",\n                                                children: \"Related Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                                children: relatedVideos.map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        video: video\n                                                    }, video.video_id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    searchResults.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"No videos found matching your search.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_RightSideNavBar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SearchPage, \"FubyhAiyB/f8Z8QYyLkL2yo94Yw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SearchPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchPage);\nvar _c;\n$RefreshReg$(_c, \"SearchPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/search/page.tsx\n"));

/***/ })

});