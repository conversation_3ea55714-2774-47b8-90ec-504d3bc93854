"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/search/page",{

/***/ "(app-pages-browser)/./app/search/page.tsx":
/*!*****************************!*\
  !*** ./app/search/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_VideoCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/VideoCard */ \"(app-pages-browser)/./components/VideoCard.tsx\");\n/* harmony import */ var _components_HomeDashboard_LeftSideNavBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/HomeDashboard/LeftSideNavBar */ \"(app-pages-browser)/./components/HomeDashboard/LeftSideNavBar.tsx\");\n/* harmony import */ var _components_HomeDashboard_RightSideNavBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/HomeDashboard/RightSideNavBar */ \"(app-pages-browser)/./components/HomeDashboard/RightSideNavBar.tsx\");\n/* harmony import */ var _components_Search_SearchPageNav__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/Search/SearchPageNav */ \"(app-pages-browser)/./components/Search/SearchPageNav.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SearchPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const query = searchParams.get('q') || '';\n    const page = parseInt(searchParams.get('page') || '1');\n    const videoType = searchParams.get('type');\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [relatedVideos, setRelatedVideos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasNextPage, setHasNextPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(query);\n    const [isRandomResults, setIsRandomResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch search results\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchPage.useEffect\": ()=>{\n            if (!query) return;\n            const fetchSearchResults = {\n                \"SearchPage.useEffect.fetchSearchResults\": async ()=>{\n                    setLoading(true);\n                    setError(null);\n                    try {\n                        // Get token from localStorage for authorization\n                        const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('auth_token') || localStorage.getItem('wedzat_token');\n                        if (!token) {\n                            setError('Authentication required. Please log in.');\n                            setLoading(false);\n                            return;\n                        }\n                        // Build the API URL with query parameters\n                        let apiUrl = \"/api/youtube-search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(page);\n                        // Add video type filter if specified\n                        if (videoType) {\n                            apiUrl += \"&type=\".concat(encodeURIComponent(videoType));\n                        }\n                        console.log('Making search request to:', apiUrl);\n                        // Try direct API call if the frontend API route fails\n                        let response;\n                        try {\n                            response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(apiUrl, {\n                                headers: {\n                                    'Authorization': \"token=\".concat(token)\n                                }\n                            });\n                        } catch (frontendApiError) {\n                            console.error('Frontend API route failed, trying direct API call:', frontendApiError);\n                            // Try direct API call to backend\n                            let directApiUrl = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/youtube-search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(page);\n                            if (videoType) {\n                                directApiUrl += \"&type=\".concat(encodeURIComponent(videoType));\n                            }\n                            console.log('Making direct API call to:', directApiUrl);\n                            response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(directApiUrl, {\n                                headers: {\n                                    'Authorization': \"token=\".concat(token)\n                                }\n                            });\n                        }\n                        console.log('Search response:', response.data);\n                        if (!response.data.search_results) {\n                            console.error('No search_results in response:', response.data);\n                            setError('Invalid response format from server');\n                            return;\n                        }\n                        setSearchResults(response.data.search_results || []);\n                        setRelatedVideos(response.data.related_videos || []);\n                        setTotalCount(response.data.total_count || 0);\n                        setCurrentPage(response.data.current_page || 1);\n                        setHasNextPage(response.data.next_page || false);\n                        setIsRandomResults(response.data.is_random_results || false);\n                        setMessage(response.data.message || null);\n                    } catch (err) {\n                        var _err_response, _err_response1, _err_response2, _err_response_data, _err_response3, _err_response_data1, _err_response4;\n                        console.error('Error fetching search results:', err);\n                        console.error('Error details:', (_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data);\n                        if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 401) {\n                            setError('Authentication required. Please log in again.');\n                        } else if (((_err_response2 = err.response) === null || _err_response2 === void 0 ? void 0 : _err_response2.status) === 403) {\n                            var _err_response_data2, _err_response5;\n                            setError(\"Authentication error: \".concat(((_err_response5 = err.response) === null || _err_response5 === void 0 ? void 0 : (_err_response_data2 = _err_response5.data) === null || _err_response_data2 === void 0 ? void 0 : _err_response_data2.message) || 'Access denied'));\n                        } else if ((_err_response3 = err.response) === null || _err_response3 === void 0 ? void 0 : (_err_response_data = _err_response3.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) {\n                            setError(\"Error: \".concat(err.response.data.error));\n                        } else if ((_err_response4 = err.response) === null || _err_response4 === void 0 ? void 0 : (_err_response_data1 = _err_response4.data) === null || _err_response_data1 === void 0 ? void 0 : _err_response_data1.message) {\n                            setError(\"Error: \".concat(err.response.data.message));\n                        } else {\n                            setError(\"Failed to fetch search results: \".concat(err.message || 'Unknown error'));\n                        }\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SearchPage.useEffect.fetchSearchResults\"];\n            fetchSearchResults();\n        }\n    }[\"SearchPage.useEffect\"], [\n        query,\n        page,\n        videoType\n    ]);\n    // Handle search form submission\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (!searchInput.trim()) return;\n        // Navigate to search page with the new query\n        router.push(\"/search?q=\".concat(encodeURIComponent(searchInput.trim())));\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage < 1) return;\n        // Navigate to the new page\n        let url = \"/search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(newPage);\n        if (videoType) {\n            url += \"&type=\".concat(encodeURIComponent(videoType));\n        }\n        router.push(url);\n    };\n    // Handle video type filter\n    const handleTypeFilter = (type)=>{\n        let url = \"/search?q=\".concat(encodeURIComponent(query));\n        if (type) {\n            url += \"&type=\".concat(encodeURIComponent(type));\n        }\n        router.push(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Search_SearchPageNav__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 overflow-hidden mt-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_LeftSideNavBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-6 md:p-8 pl-8 md:pl-10 pr-8 md:pr-10 pt-8 md:pt-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative max-w-4xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchInput,\n                                            onChange: (e)=>setSearchInput(e.target.value),\n                                            placeholder: \"Search for videos...\",\n                                            className: \"w-full pl-12 pr-24 py-4 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 text-black shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"absolute left-4 top-4 h-6 w-6 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"absolute right-3 top-2.5 px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors font-medium\",\n                                            children: \"Search\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-3 mb-8 max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter(null),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(!videoType ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"All Videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('flash'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'flash' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Flashes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('glimpse'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'glimpse' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Glimpses\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('movie'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'movie' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Movies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('story'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'story' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Stories\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col justify-center items-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-12 w-12 animate-spin text-red-600 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Searching for videos...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md mx-auto my-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 text-red-700 p-6 rounded-lg shadow-sm border border-red-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold text-lg mb-2\",\n                                            children: \"Error:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mb-4\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>window.location.reload(),\n                                                    className: \"w-full py-3 bg-red-600 text-white rounded-md hover:bg-red-700 font-medium transition-colors\",\n                                                    children: \"Reload Page\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setError(null);\n                                                        setLoading(true);\n                                                        // Trigger a new search with the current parameters\n                                                        const url = \"/search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(page).concat(videoType ? \"&type=\".concat(encodeURIComponent(videoType)) : '');\n                                                        router.push(url);\n                                                    },\n                                                    className: \"w-full py-3 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 font-medium transition-colors\",\n                                                    children: \"Try Again\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 max-w-4xl mx-auto\",\n                                        children: isRandomResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-semibold\",\n                                                    children: [\n                                                        'No results found for \"',\n                                                        query,\n                                                        '\"'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mt-2 text-lg\",\n                                                    children: message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold\",\n                                            children: [\n                                                totalCount,\n                                                \" \",\n                                                totalCount === 1 ? 'result' : 'results',\n                                                ' for \"',\n                                                query,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                        children: searchResults.map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                video: video\n                                            }, video.video_id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mt-10 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(currentPage - 1),\n                                                disabled: currentPage === 1,\n                                                className: \"px-6 py-2.5 rounded-md bg-gray-100 text-gray-700 disabled:opacity-50 hover:bg-gray-200 font-medium transition-colors\",\n                                                children: \"Previous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-6 py-2.5 bg-gray-50 rounded-md font-medium\",\n                                                children: [\n                                                    \"Page \",\n                                                    currentPage\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(currentPage + 1),\n                                                disabled: !hasNextPage,\n                                                className: \"px-6 py-2.5 rounded-md bg-gray-100 text-gray-700 disabled:opacity-50 hover:bg-gray-200 font-medium transition-colors\",\n                                                children: \"Next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    relatedVideos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-16 pt-8 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold mb-6 max-w-4xl mx-auto\",\n                                                children: \"Related Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                                children: relatedVideos.map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        video: video\n                                                    }, video.video_id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    searchResults.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-16 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-8 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-lg\",\n                                                    children: \"No videos found matching your search.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 mt-2\",\n                                                    children: \"Try different keywords or browse categories.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_RightSideNavBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SearchPage, \"g9w25N50IzxMvJK394K6hUHRbRI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SearchPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchPage);\nvar _c;\n$RefreshReg$(_c, \"SearchPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/search/page.tsx\n"));

/***/ })

});