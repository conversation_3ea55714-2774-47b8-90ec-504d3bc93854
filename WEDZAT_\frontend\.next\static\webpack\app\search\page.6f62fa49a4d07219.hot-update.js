"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/search/page",{

/***/ "(app-pages-browser)/./app/search/page.tsx":
/*!*****************************!*\
  !*** ./app/search/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_VideoCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/VideoCard */ \"(app-pages-browser)/./components/VideoCard.tsx\");\n/* harmony import */ var _components_HomeDashboard_LeftSideNavBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/HomeDashboard/LeftSideNavBar */ \"(app-pages-browser)/./components/HomeDashboard/LeftSideNavBar.tsx\");\n/* harmony import */ var _components_HomeDashboard_RightSideNavBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/HomeDashboard/RightSideNavBar */ \"(app-pages-browser)/./components/HomeDashboard/RightSideNavBar.tsx\");\n/* harmony import */ var _components_Search_SearchPageNav__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/Search/SearchPageNav */ \"(app-pages-browser)/./components/Search/SearchPageNav.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SearchPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const query = searchParams.get('q') || '';\n    const page = parseInt(searchParams.get('page') || '1');\n    const videoType = searchParams.get('type');\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [relatedVideos, setRelatedVideos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasNextPage, setHasNextPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(query);\n    const [isRandomResults, setIsRandomResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch search results\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchPage.useEffect\": ()=>{\n            if (!query) return;\n            const fetchSearchResults = {\n                \"SearchPage.useEffect.fetchSearchResults\": async ()=>{\n                    setLoading(true);\n                    setError(null);\n                    try {\n                        // Get token from localStorage for authorization\n                        const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('auth_token') || localStorage.getItem('wedzat_token');\n                        if (!token) {\n                            console.error('No authentication token found in localStorage');\n                            setError('Authentication required. Please log in.');\n                            setLoading(false);\n                            return;\n                        }\n                        console.log('Using token for search:', token.substring(0, 10) + '...');\n                        // Build the API URL with query parameters\n                        let apiUrl = \"/api/youtube-search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(page);\n                        // Add video type filter if specified\n                        if (videoType) {\n                            apiUrl += \"&type=\".concat(encodeURIComponent(videoType));\n                        }\n                        console.log('Making search request to:', apiUrl);\n                        // Try direct API call if the frontend API route fails\n                        let response;\n                        try {\n                            response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(apiUrl, {\n                                headers: {\n                                    'Authorization': \"Bearer \".concat(token)\n                                }\n                            });\n                        } catch (frontendApiError) {\n                            console.error('Frontend API route failed, trying direct API call:', frontendApiError);\n                            // Try direct API call to backend\n                            let directApiUrl = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/youtube-search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(page);\n                            if (videoType) {\n                                directApiUrl += \"&type=\".concat(encodeURIComponent(videoType));\n                            }\n                            console.log('Making direct API call to:', directApiUrl);\n                            response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(directApiUrl, {\n                                headers: {\n                                    'Authorization': \"Bearer \".concat(token)\n                                }\n                            });\n                        }\n                        console.log('Search response:', response.data);\n                        if (!response.data.search_results) {\n                            console.error('No search_results in response:', response.data);\n                            setError('Invalid response format from server');\n                            return;\n                        }\n                        setSearchResults(response.data.search_results || []);\n                        setRelatedVideos(response.data.related_videos || []);\n                        setTotalCount(response.data.total_count || 0);\n                        setCurrentPage(response.data.current_page || 1);\n                        setHasNextPage(response.data.next_page || false);\n                        setIsRandomResults(response.data.is_random_results || false);\n                        setMessage(response.data.message || null);\n                    } catch (err) {\n                        var _err_response, _err_response1, _err_response2, _err_response_data, _err_response3, _err_response_data1, _err_response4;\n                        console.error('Error fetching search results:', err);\n                        console.error('Error details:', (_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data);\n                        if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 401) {\n                            setError('Authentication required. Please log in again.');\n                        } else if (((_err_response2 = err.response) === null || _err_response2 === void 0 ? void 0 : _err_response2.status) === 403) {\n                            var _err_response_data2, _err_response5;\n                            setError(\"Authentication error: \".concat(((_err_response5 = err.response) === null || _err_response5 === void 0 ? void 0 : (_err_response_data2 = _err_response5.data) === null || _err_response_data2 === void 0 ? void 0 : _err_response_data2.message) || 'Access denied'));\n                        } else if ((_err_response3 = err.response) === null || _err_response3 === void 0 ? void 0 : (_err_response_data = _err_response3.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) {\n                            setError(\"Error: \".concat(err.response.data.error));\n                        } else if ((_err_response4 = err.response) === null || _err_response4 === void 0 ? void 0 : (_err_response_data1 = _err_response4.data) === null || _err_response_data1 === void 0 ? void 0 : _err_response_data1.message) {\n                            setError(\"Error: \".concat(err.response.data.message));\n                        } else {\n                            setError(\"Failed to fetch search results: \".concat(err.message || 'Unknown error'));\n                        }\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SearchPage.useEffect.fetchSearchResults\"];\n            fetchSearchResults();\n        }\n    }[\"SearchPage.useEffect\"], [\n        query,\n        page,\n        videoType\n    ]);\n    // Handle search form submission\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (!searchInput.trim()) return;\n        // Navigate to search page with the new query\n        router.push(\"/search?q=\".concat(encodeURIComponent(searchInput.trim())));\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage < 1) return;\n        // Navigate to the new page\n        let url = \"/search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(newPage);\n        if (videoType) {\n            url += \"&type=\".concat(encodeURIComponent(videoType));\n        }\n        router.push(url);\n    };\n    // Handle video type filter\n    const handleTypeFilter = (type)=>{\n        let url = \"/search?q=\".concat(encodeURIComponent(query));\n        if (type) {\n            url += \"&type=\".concat(encodeURIComponent(type));\n        }\n        router.push(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Search_SearchPageNav__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 overflow-hidden mt-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_LeftSideNavBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-6 md:p-8 pl-8 md:pl-10 pr-8 md:pr-10 pt-8 md:pt-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative max-w-4xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchInput,\n                                            onChange: (e)=>setSearchInput(e.target.value),\n                                            placeholder: \"Search for videos...\",\n                                            className: \"w-full pl-12 pr-24 py-4 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 text-black shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"absolute left-4 top-4 h-6 w-6 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"absolute right-3 top-2.5 px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors font-medium\",\n                                            children: \"Search\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-3 mb-8 max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter(null),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(!videoType ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"All Videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('flash'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'flash' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Flashes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('glimpse'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'glimpse' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Glimpses\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('movie'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'movie' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Movies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('story'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'story' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Stories\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col justify-center items-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-12 w-12 animate-spin text-red-600 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Searching for videos...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md mx-auto my-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 text-red-700 p-6 rounded-lg shadow-sm border border-red-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold text-lg mb-2\",\n                                            children: \"Error:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mb-4\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>window.location.reload(),\n                                                    className: \"w-full py-3 bg-red-600 text-white rounded-md hover:bg-red-700 font-medium transition-colors\",\n                                                    children: \"Reload Page\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setError(null);\n                                                        setLoading(true);\n                                                        // Trigger a new search with the current parameters\n                                                        const url = \"/search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(page).concat(videoType ? \"&type=\".concat(encodeURIComponent(videoType)) : '');\n                                                        router.push(url);\n                                                    },\n                                                    className: \"w-full py-3 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 font-medium transition-colors\",\n                                                    children: \"Try Again\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 max-w-4xl mx-auto\",\n                                        children: isRandomResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-semibold\",\n                                                    children: [\n                                                        'No results found for \"',\n                                                        query,\n                                                        '\"'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mt-2 text-lg\",\n                                                    children: message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold\",\n                                            children: [\n                                                totalCount,\n                                                \" \",\n                                                totalCount === 1 ? 'result' : 'results',\n                                                ' for \"',\n                                                query,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                        children: searchResults.map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                video: video\n                                            }, video.video_id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mt-10 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(currentPage - 1),\n                                                disabled: currentPage === 1,\n                                                className: \"px-6 py-2.5 rounded-md bg-gray-100 text-gray-700 disabled:opacity-50 hover:bg-gray-200 font-medium transition-colors\",\n                                                children: \"Previous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-6 py-2.5 bg-gray-50 rounded-md font-medium\",\n                                                children: [\n                                                    \"Page \",\n                                                    currentPage\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(currentPage + 1),\n                                                disabled: !hasNextPage,\n                                                className: \"px-6 py-2.5 rounded-md bg-gray-100 text-gray-700 disabled:opacity-50 hover:bg-gray-200 font-medium transition-colors\",\n                                                children: \"Next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    relatedVideos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-16 pt-8 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold mb-6 max-w-4xl mx-auto\",\n                                                children: \"Related Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                                children: relatedVideos.map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        video: video\n                                                    }, video.video_id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    searchResults.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-16 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-8 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-lg\",\n                                                    children: \"No videos found matching your search.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 mt-2\",\n                                                    children: \"Try different keywords or browse categories.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_RightSideNavBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SearchPage, \"g9w25N50IzxMvJK394K6hUHRbRI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SearchPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchPage);\nvar _c;\n$RefreshReg$(_c, \"SearchPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/search/page.tsx\n"));

/***/ })

});