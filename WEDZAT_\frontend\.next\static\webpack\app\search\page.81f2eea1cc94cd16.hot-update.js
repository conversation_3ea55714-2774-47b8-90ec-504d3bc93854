"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/search/page",{

/***/ "(app-pages-browser)/./app/search/page.tsx":
/*!*****************************!*\
  !*** ./app/search/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_VideoCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/VideoCard */ \"(app-pages-browser)/./components/VideoCard.tsx\");\n/* harmony import */ var _components_HomeDashboard_TopNav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/HomeDashboard/TopNav */ \"(app-pages-browser)/./components/HomeDashboard/TopNav.tsx\");\n/* harmony import */ var _components_HomeDashboard_LeftSideNavBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/HomeDashboard/LeftSideNavBar */ \"(app-pages-browser)/./components/HomeDashboard/LeftSideNavBar.tsx\");\n/* harmony import */ var _components_HomeDashboard_RightSideNavBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/HomeDashboard/RightSideNavBar */ \"(app-pages-browser)/./components/HomeDashboard/RightSideNavBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SearchPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const query = searchParams.get('q') || '';\n    const page = parseInt(searchParams.get('page') || '1');\n    const videoType = searchParams.get('type');\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [relatedVideos, setRelatedVideos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasNextPage, setHasNextPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(query);\n    // Fetch search results\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchPage.useEffect\": ()=>{\n            if (!query) return;\n            const fetchSearchResults = {\n                \"SearchPage.useEffect.fetchSearchResults\": async ()=>{\n                    setLoading(true);\n                    setError(null);\n                    try {\n                        // Get token from localStorage for authorization\n                        const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('auth_token') || localStorage.getItem('wedzat_token');\n                        if (!token) {\n                            setError('Authentication required. Please log in.');\n                            setLoading(false);\n                            return;\n                        }\n                        // Build the API URL with query parameters\n                        let apiUrl = \"/api/youtube-search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(page);\n                        // Add video type filter if specified\n                        if (videoType) {\n                            apiUrl += \"&type=\".concat(encodeURIComponent(videoType));\n                        }\n                        console.log('Making search request to:', apiUrl);\n                        // Try direct API call if the frontend API route fails\n                        let response;\n                        try {\n                            response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(apiUrl, {\n                                headers: {\n                                    'Authorization': \"Bearer \".concat(token)\n                                }\n                            });\n                        } catch (frontendApiError) {\n                            console.error('Frontend API route failed, trying direct API call:', frontendApiError);\n                            // Try direct API call to backend\n                            let directApiUrl = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/youtube-search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(page);\n                            if (videoType) {\n                                directApiUrl += \"&type=\".concat(encodeURIComponent(videoType));\n                            }\n                            console.log('Making direct API call to:', directApiUrl);\n                            response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(directApiUrl, {\n                                headers: {\n                                    'Authorization': \"Bearer \".concat(token)\n                                }\n                            });\n                        }\n                        console.log('Search response:', response.data);\n                        if (!response.data.search_results) {\n                            console.error('No search_results in response:', response.data);\n                            setError('Invalid response format from server');\n                            return;\n                        }\n                        setSearchResults(response.data.search_results || []);\n                        setRelatedVideos(response.data.related_videos || []);\n                        setTotalCount(response.data.total_count || 0);\n                        setCurrentPage(response.data.current_page || 1);\n                        setHasNextPage(response.data.next_page || false);\n                    } catch (err) {\n                        var _err_response, _err_response1, _err_response_data, _err_response2;\n                        console.error('Error fetching search results:', err);\n                        console.error('Error details:', (_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data);\n                        if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 401) {\n                            setError('Authentication required. Please log in again.');\n                        } else if ((_err_response2 = err.response) === null || _err_response2 === void 0 ? void 0 : (_err_response_data = _err_response2.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) {\n                            setError(\"Error: \".concat(err.response.data.error));\n                        } else {\n                            setError(\"Failed to fetch search results: \".concat(err.message || 'Unknown error'));\n                        }\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SearchPage.useEffect.fetchSearchResults\"];\n            fetchSearchResults();\n        }\n    }[\"SearchPage.useEffect\"], [\n        query,\n        page,\n        videoType\n    ]);\n    // Handle search form submission\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (!searchInput.trim()) return;\n        // Navigate to search page with the new query\n        router.push(\"/search?q=\".concat(encodeURIComponent(searchInput.trim())));\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage < 1) return;\n        // Navigate to the new page\n        let url = \"/search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(newPage);\n        if (videoType) {\n            url += \"&type=\".concat(encodeURIComponent(videoType));\n        }\n        router.push(url);\n    };\n    // Handle video type filter\n    const handleTypeFilter = (type)=>{\n        let url = \"/search?q=\".concat(encodeURIComponent(query));\n        if (type) {\n            url += \"&type=\".concat(encodeURIComponent(type));\n        }\n        router.push(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_TopNav__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_LeftSideNavBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-4 md:p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchInput,\n                                            onChange: (e)=>setSearchInput(e.target.value),\n                                            placeholder: \"Search for videos...\",\n                                            className: \"w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 text-black\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"absolute left-3 top-3.5 h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"absolute right-3 top-2 px-4 py-1.5 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\",\n                                            children: \"Search\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter(null),\n                                        className: \"px-4 py-2 rounded-full text-sm \".concat(!videoType ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                        children: \"All Videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('flash'),\n                                        className: \"px-4 py-2 rounded-full text-sm \".concat(videoType === 'flash' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                        children: \"Flashes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('glimpse'),\n                                        className: \"px-4 py-2 rounded-full text-sm \".concat(videoType === 'glimpse' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                        children: \"Glimpses\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('movie'),\n                                        className: \"px-4 py-2 rounded-full text-sm \".concat(videoType === 'movie' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                        children: \"Movies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('story'),\n                                        className: \"px-4 py-2 rounded-full text-sm \".concat(videoType === 'story' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                        children: \"Stories\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-8 w-8 animate-spin text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-100 text-red-700 p-4 rounded-md mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold\",\n                                        children: \"Error:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.reload(),\n                                        className: \"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\",\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: [\n                                                totalCount,\n                                                \" \",\n                                                totalCount === 1 ? 'result' : 'results',\n                                                ' for \"',\n                                                query,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: searchResults.map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                video: video\n                                            }, video.video_id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mt-8 gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(currentPage - 1),\n                                                disabled: currentPage === 1,\n                                                className: \"px-4 py-2 rounded-md bg-gray-200 text-gray-700 disabled:opacity-50\",\n                                                children: \"Previous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-4 py-2\",\n                                                children: [\n                                                    \"Page \",\n                                                    currentPage\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(currentPage + 1),\n                                                disabled: !hasNextPage,\n                                                className: \"px-4 py-2 rounded-md bg-gray-200 text-gray-700 disabled:opacity-50\",\n                                                children: \"Next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    relatedVideos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold mb-4\",\n                                                children: \"Related Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                                children: relatedVideos.map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        video: video\n                                                    }, video.video_id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    searchResults.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"No videos found matching your search.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_RightSideNavBar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SearchPage, \"FubyhAiyB/f8Z8QYyLkL2yo94Yw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SearchPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchPage);\nvar _c;\n$RefreshReg$(_c, \"SearchPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/search/page.tsx\n"));

/***/ })

});