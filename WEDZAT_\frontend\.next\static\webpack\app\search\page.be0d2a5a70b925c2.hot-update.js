"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/search/page",{

/***/ "(app-pages-browser)/./app/search/page.tsx":
/*!*****************************!*\
  !*** ./app/search/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_VideoCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/VideoCard */ \"(app-pages-browser)/./components/VideoCard.tsx\");\n/* harmony import */ var _components_HomeDashboard_TopNav__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/HomeDashboard/TopNav */ \"(app-pages-browser)/./components/HomeDashboard/TopNav.tsx\");\n/* harmony import */ var _components_HomeDashboard_LeftSideNavBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/HomeDashboard/LeftSideNavBar */ \"(app-pages-browser)/./components/HomeDashboard/LeftSideNavBar.tsx\");\n/* harmony import */ var _components_HomeDashboard_RightSideNavBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/HomeDashboard/RightSideNavBar */ \"(app-pages-browser)/./components/HomeDashboard/RightSideNavBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SearchPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const query = searchParams.get('q') || '';\n    const page = parseInt(searchParams.get('page') || '1');\n    const videoType = searchParams.get('type');\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [relatedVideos, setRelatedVideos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasNextPage, setHasNextPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(query);\n    const [isRandomResults, setIsRandomResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch search results\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SearchPage.useEffect\": ()=>{\n            if (!query) return;\n            const fetchSearchResults = {\n                \"SearchPage.useEffect.fetchSearchResults\": async ()=>{\n                    setLoading(true);\n                    setError(null);\n                    try {\n                        // Get token from localStorage for authorization\n                        const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('auth_token') || localStorage.getItem('wedzat_token');\n                        if (!token) {\n                            setError('Authentication required. Please log in.');\n                            setLoading(false);\n                            return;\n                        }\n                        // Build the API URL with query parameters\n                        let apiUrl = \"/api/youtube-search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(page);\n                        // Add video type filter if specified\n                        if (videoType) {\n                            apiUrl += \"&type=\".concat(encodeURIComponent(videoType));\n                        }\n                        console.log('Making search request to:', apiUrl);\n                        // Try direct API call if the frontend API route fails\n                        let response;\n                        try {\n                            response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(apiUrl, {\n                                headers: {\n                                    'Authorization': \"Bearer \".concat(token)\n                                }\n                            });\n                        } catch (frontendApiError) {\n                            console.error('Frontend API route failed, trying direct API call:', frontendApiError);\n                            // Try direct API call to backend\n                            let directApiUrl = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/youtube-search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(page);\n                            if (videoType) {\n                                directApiUrl += \"&type=\".concat(encodeURIComponent(videoType));\n                            }\n                            console.log('Making direct API call to:', directApiUrl);\n                            response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(directApiUrl, {\n                                headers: {\n                                    'Authorization': \"Bearer \".concat(token)\n                                }\n                            });\n                        }\n                        console.log('Search response:', response.data);\n                        if (!response.data.search_results) {\n                            console.error('No search_results in response:', response.data);\n                            setError('Invalid response format from server');\n                            return;\n                        }\n                        setSearchResults(response.data.search_results || []);\n                        setRelatedVideos(response.data.related_videos || []);\n                        setTotalCount(response.data.total_count || 0);\n                        setCurrentPage(response.data.current_page || 1);\n                        setHasNextPage(response.data.next_page || false);\n                        setIsRandomResults(response.data.is_random_results || false);\n                        setMessage(response.data.message || null);\n                    } catch (err) {\n                        var _err_response, _err_response1, _err_response_data, _err_response2;\n                        console.error('Error fetching search results:', err);\n                        console.error('Error details:', (_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data);\n                        if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 401) {\n                            setError('Authentication required. Please log in again.');\n                        } else if ((_err_response2 = err.response) === null || _err_response2 === void 0 ? void 0 : (_err_response_data = _err_response2.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) {\n                            setError(\"Error: \".concat(err.response.data.error));\n                        } else {\n                            setError(\"Failed to fetch search results: \".concat(err.message || 'Unknown error'));\n                        }\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SearchPage.useEffect.fetchSearchResults\"];\n            fetchSearchResults();\n        }\n    }[\"SearchPage.useEffect\"], [\n        query,\n        page,\n        videoType\n    ]);\n    // Handle search form submission\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (!searchInput.trim()) return;\n        // Navigate to search page with the new query\n        router.push(\"/search?q=\".concat(encodeURIComponent(searchInput.trim())));\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage < 1) return;\n        // Navigate to the new page\n        let url = \"/search?q=\".concat(encodeURIComponent(query), \"&page=\").concat(newPage);\n        if (videoType) {\n            url += \"&type=\".concat(encodeURIComponent(videoType));\n        }\n        router.push(url);\n    };\n    // Handle video type filter\n    const handleTypeFilter = (type)=>{\n        let url = \"/search?q=\".concat(encodeURIComponent(query));\n        if (type) {\n            url += \"&type=\".concat(encodeURIComponent(type));\n        }\n        router.push(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_TopNav__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_LeftSideNavBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-6 md:p-8 pl-8 md:pl-10 pr-8 md:pr-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative max-w-4xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchInput,\n                                            onChange: (e)=>setSearchInput(e.target.value),\n                                            placeholder: \"Search for videos...\",\n                                            className: \"w-full pl-12 pr-24 py-4 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 text-black shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"absolute left-4 top-4 h-6 w-6 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"absolute right-3 top-2.5 px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors font-medium\",\n                                            children: \"Search\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-3 mb-8 max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter(null),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(!videoType ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"All Videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('flash'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'flash' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Flashes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('glimpse'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'glimpse' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Glimpses\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('movie'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'movie' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Movies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleTypeFilter('story'),\n                                        className: \"px-5 py-2.5 rounded-full text-sm font-medium \".concat(videoType === 'story' ? 'bg-red-600 text-white shadow-sm' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: \"Stories\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col justify-center items-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-12 w-12 animate-spin text-red-600 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Searching for videos...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md mx-auto my-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 text-red-700 p-6 rounded-lg shadow-sm border border-red-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold text-lg mb-2\",\n                                            children: \"Error:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mb-4\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>window.location.reload(),\n                                            className: \"w-full py-3 bg-red-600 text-white rounded-md hover:bg-red-700 font-medium transition-colors\",\n                                            children: \"Try Again\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 max-w-4xl mx-auto\",\n                                        children: isRandomResults ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-semibold\",\n                                                    children: [\n                                                        'No results found for \"',\n                                                        query,\n                                                        '\"'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mt-2 text-lg\",\n                                                    children: message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold\",\n                                            children: [\n                                                totalCount,\n                                                \" \",\n                                                totalCount === 1 ? 'result' : 'results',\n                                                ' for \"',\n                                                query,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                        children: searchResults.map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                video: video\n                                            }, video.video_id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mt-10 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(currentPage - 1),\n                                                disabled: currentPage === 1,\n                                                className: \"px-6 py-2.5 rounded-md bg-gray-100 text-gray-700 disabled:opacity-50 hover:bg-gray-200 font-medium transition-colors\",\n                                                children: \"Previous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-6 py-2.5 bg-gray-50 rounded-md font-medium\",\n                                                children: [\n                                                    \"Page \",\n                                                    currentPage\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(currentPage + 1),\n                                                disabled: !hasNextPage,\n                                                className: \"px-6 py-2.5 rounded-md bg-gray-100 text-gray-700 disabled:opacity-50 hover:bg-gray-200 font-medium transition-colors\",\n                                                children: \"Next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    relatedVideos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-16 pt-8 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold mb-6 max-w-4xl mx-auto\",\n                                                children: \"Related Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                                children: relatedVideos.map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        video: video\n                                                    }, video.video_id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    searchResults.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-16 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-8 shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-lg\",\n                                                    children: \"No videos found matching your search.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 mt-2\",\n                                                    children: \"Try different keywords or browse categories.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_RightSideNavBar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\wedzatttt\\\\WEDZAT_\\\\frontend\\\\app\\\\search\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SearchPage, \"g9w25N50IzxMvJK394K6hUHRbRI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SearchPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchPage);\nvar _c;\n$RefreshReg$(_c, \"SearchPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/search/page.tsx\n"));

/***/ })

});