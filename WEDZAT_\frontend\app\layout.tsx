"use client";

// Metada<PERSON> is now in a separate file
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON> } from "next/font/google";
import "./globals.css";
import { <PERSON><PERSON>rov<PERSON> } from "@clerk/nextjs";
import { AuthProvider } from "../contexts/AuthContext";
import { UploadProvider } from "../contexts/UploadContexts";
import { GlobalUploadProvider } from "../contexts/GlobalUploadManager";
import <PERSON>ript from "next/script";
import { useEffect, useState } from "react";
// Import camera utils to initialize global camera

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const inter = Inter({ subsets: ["latin"] });

// Metadata is now in metadata.ts

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Use client-side only rendering for the body content to avoid hydration mismatches
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <ClerkProvider
      appearance={{
        // Add custom appearance options to avoid hydration issues
        variables: {
          colorPrimary: "#b31b1e",
        },
        elements: {
          // Add data-hydration-safe attributes to avoid hydration issues
          rootBox: {
            attributes: {
              "data-hydration-safe": "true",
            },
          },
          card: {
            attributes: {
              "data-hydration-safe": "true",
            },
          },
        },
      }}
      // Use the new redirect props format
      redirectUrl="/auth/callback"
      signInUrl="/"
      signUpUrl="/"
    >
      <html lang="en">
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
          suppressHydrationWarning // Add this to suppress hydration warnings
        >
          {/* Use a more resilient approach to avoid hydration issues */}
          <div suppressHydrationWarning>
            {!mounted ? (
              <div className="flex flex-col justify-center items-center h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-700"></div>
                <div className="text-gray-600 mt-4">
                  Loading your account...
                </div>
              </div>
            ) : null}

            <div style={{ display: mounted ? "block" : "none" }}>
              <AuthProvider>
                <UploadProvider>
                  <GlobalUploadProvider>
                  {children}
                </GlobalUploadProvider>
                </UploadProvider>
              </AuthProvider>
            </div>
          </div>

          {/* Script to sync localStorage token to cookie for middleware */}
          <Script id="sync-token-to-cookie" strategy="afterInteractive">
            {`
              function syncTokenToCookie() {
                try {
                  const token = localStorage.getItem('wedzat_token') || localStorage.getItem('token') || localStorage.getItem('jwt_token');
                  if (token) {
                    document.cookie = 'wedzat_token=' + token + '; path=/; max-age=86400; SameSite=Lax';
                    console.log('Token synced to cookie for middleware');
                  }

                  // Also sync vendor flag
                  const isVendor = localStorage.getItem('is_vendor');
                  if (isVendor === 'true') {
                    document.cookie = 'is_vendor=true; path=/; max-age=86400; SameSite=Lax';
                    console.log('Vendor flag synced to cookie for middleware');
                  }
                } catch (e) {
                  console.error('Error syncing token to cookie:', e);
                }
              }

              // Run on page load
              syncTokenToCookie();

              // Also run when localStorage changes
              window.addEventListener('storage', syncTokenToCookie);
            `}
          </Script>

          {/* Script to clean up browser extension attributes */}
          <Script id="cleanup-extension-attrs" strategy="beforeInteractive">
            {`
              (function() {
                // Run immediately to clean up before React hydration
                function cleanupExtensionAttributes() {
                  // Target common extension attributes
                  const attributesToRemove = [
                    'bis_skin_checked',
                    '__processed_',
                    'data-bis-'
                  ];

                  // Get all elements
                  const allElements = document.querySelectorAll('*');

                  // Remove attributes from each element
                  allElements.forEach(el => {
                    for (let i = 0; i < el.attributes.length; i++) {
                      const attr = el.attributes[i];
                      for (const badAttr of attributesToRemove) {
                        if (attr.name.includes(badAttr)) {
                          el.removeAttribute(attr.name);
                          // Adjust index since we removed an attribute
                          i--;
                          break;
                        }
                      }
                    }
                  });
                }

                // Run immediately
                cleanupExtensionAttributes();

                // Also run after a short delay to catch any late additions
                setTimeout(cleanupExtensionAttributes, 0);
              })();
            `}
          </Script>

          {/* Script to initialize camera and prevent locking */}
          <Script id="init-camera" strategy="afterInteractive">
            {`
              // This script helps ensure the camera is never locked
              // by creating a persistent camera stream
              console.log('Camera initialization script loaded');
            `}
          </Script>

          {/* Botpress webchat is now handled by the useBotpressInitializer hook */}
        </body>
      </html>
    </ClerkProvider>
  );
}
