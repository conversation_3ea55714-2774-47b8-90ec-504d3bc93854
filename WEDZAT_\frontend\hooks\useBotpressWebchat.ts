"use client";

import { useEffect, useState } from 'react';

interface BotpressConfig {
  botId?: string;
  hostUrl?: string;
  messagingUrl?: string;
  clientId?: string;
  stylesheet?: string;
  hideWidget?: boolean;
  disableAnimations?: boolean;
  closeOnEscape?: boolean;
  showConversationsButton?: boolean;
  enableTranscriptDownload?: boolean;
  theme?: 'default' | 'dark' | 'light' | 'purple';
}

export function useBotpressWebchat({
  botId = "8AYDGFUF", // Default bot ID from the existing config
  hostUrl = "https://cdn.botpress.cloud/webchat/v2.3",
  messagingUrl = "https://messaging.botpress.cloud",
  clientId = "8AYDGFUF-8AYDGFUF",
  stylesheet,
  hideWidget = false,
  disableAnimations = false,
  closeOnEscape = true,
  showConversationsButton = false,
  enableTranscriptDownload = false,
  theme = "default",
}: BotpressConfig = {}) {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined' || isInitialized) return;

    // Create and inject the script elements
    const injectScript = document.createElement('script');
    injectScript.src = `${hostUrl}/inject.js`;
    injectScript.async = true;
    document.head.appendChild(injectScript);

    // Create a custom config script
    const configScript = document.createElement('script');
    configScript.innerHTML = `
      window.botpressWebChat = {
        init: function(options) {
          this.options = options || {};
          return this;
        },
        sendEvent: function(event) {
          if (event && event.type === 'show') {
            try {
              const botpressButton = document.querySelector('.bp-widget-button');
              if (botpressButton) {
                botpressButton.click();
                console.log('Clicked Botpress button');
              } else {
                console.error('Could not find Botpress button');
              }
            } catch (error) {
              console.error('Error sending event to Botpress webchat:', error);
            }
          }
        }
      };

      // Initialize with config
      window.botpressWebChat.init({
        botId: "${botId}",
        hostUrl: "${hostUrl}",
        messagingUrl: "${messagingUrl}",
        clientId: "${clientId}",
        stylesheet: "${stylesheet || ''}",
        hideWidget: ${hideWidget},
        disableAnimations: ${disableAnimations},
        closeOnEscape: ${closeOnEscape},
        showConversationsButton: ${showConversationsButton},
        enableTranscriptDownload: ${enableTranscriptDownload},
        theme: "${theme === 'default' ? '' : theme}"
      });

      // Define the global openBotpressChat function
      window.openBotpressChat = function() {
        if (window.botpressWebChat) {
          window.botpressWebChat.sendEvent({ type: 'show' });
          console.log('Botpress webchat opened successfully');
        } else {
          console.error('Botpress webchat not initialized');
        }
      };
    `;
    document.head.appendChild(configScript);

    // Load the actual config script
    const botpressConfigScript = document.createElement('script');
    botpressConfigScript.src = 'https://files.bpcontent.cloud/2025/04/23/14/20250423141656-8AYDGFUF.js';
    botpressConfigScript.async = true;
    document.head.appendChild(botpressConfigScript);

    setIsInitialized(true);

    // Cleanup function
    return () => {
      try {
        document.head.removeChild(injectScript);
        document.head.removeChild(configScript);
        document.head.removeChild(botpressConfigScript);
      } catch (error) {
        console.error('Error cleaning up Botpress scripts:', error);
      }
    };
  }, [
    botId,
    hostUrl,
    messagingUrl,
    clientId,
    stylesheet,
    hideWidget,
    disableAnimations,
    closeOnEscape,
    showConversationsButton,
    enableTranscriptDownload,
    theme,
    isInitialized
  ]);

  return { isInitialized };
}
